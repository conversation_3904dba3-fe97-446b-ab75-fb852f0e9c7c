<template>
  <div class="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 dark:from-gray-900 dark:to-gray-800">
    <UContainer class="py-12">
      <!-- Header -->
      <div class="text-center mb-12">
        <h1 class="text-4xl md:text-6xl font-bold text-gray-900 dark:text-white mb-4">
          Math Assessment Center
        </h1>
        <p class="text-xl text-gray-600 dark:text-gray-300 max-w-2xl mx-auto">
          Teacher-led assessment tools for mathematical notation and function analysis.
          Choose your assessment mode and begin instruction!
        </p>
      </div>

      <!-- Assessment Mode Selection -->
      <div class="grid md:grid-cols-2 gap-8 max-w-4xl mx-auto">
        <!-- Notation Conversion Game -->
        <UCard
          class="hover:shadow-xl transition-all duration-300 transform hover:-translate-y-1 p-8 ring-1 ring-gray-200 dark:ring-gray-700 shadow-lg"
        >
          <template #header>
            <div class="flex items-center space-x-3">
              <div class="w-12 h-12 bg-blue-100 dark:bg-blue-900 rounded-lg flex items-center justify-center">
                <Icon name="i-lucide-graduation-cap" class="w-6 h-6 text-blue-600 dark:text-blue-400" />
              </div>
              <h2 class="text-2xl font-bold text-gray-900 dark:text-white">
                Notation Conversion
              </h2>
            </div>
          </template>

          <div class="space-y-4">
            <p class="text-gray-600 dark:text-gray-300">
              Teacher-led display tool for notation conversion assessment.
              Perfect for classroom instruction and student evaluation.
            </p>

            <div class="space-y-2">
              <div class="flex items-center space-x-2 text-sm text-gray-500 dark:text-gray-400">
                <Icon name="i-lucide-check-circle" class="w-4 h-4 text-green-500" />
                <span>Algebraic ↔ Interval notation</span>
              </div>
              <div class="flex items-center space-x-2 text-sm text-gray-500 dark:text-gray-400">
                <Icon name="i-lucide-check-circle" class="w-4 h-4 text-green-500" />
                <span>Set-builder ↔ Interval notation</span>
              </div>
              <div class="flex items-center space-x-2 text-sm text-gray-500 dark:text-gray-400">
                <Icon name="i-lucide-check-circle" class="w-4 h-4 text-green-500" />
                <span>Random problem generation</span>
              </div>
            </div>

            <UButton
              @click="startGame('notation-conversion')"
              size="lg"
              class="w-full"
              :loading="loading"
            >
              Start Assessment Tool
            </UButton>
          </div>
        </UCard>

        <!-- Domain and Range Game -->
        <UCard
          class="hover:shadow-xl transition-all duration-300 transform hover:-translate-y-1 p-8 ring-1 ring-gray-200 dark:ring-gray-700 shadow-lg"
        >
          <template #header>
            <div class="flex items-center space-x-3">
              <div class="w-12 h-12 bg-green-100 dark:bg-green-900 rounded-lg flex items-center justify-center">
                <Icon name="i-lucide-bar-chart-3" class="w-6 h-6 text-green-600 dark:text-green-400" />
              </div>
              <h2 class="text-2xl font-bold text-gray-900 dark:text-white">
                Domain & Range
              </h2>
            </div>
          </template>

          <div class="space-y-4">
            <p class="text-gray-600 dark:text-gray-300">
              Teacher-led display tool for domain and range assessment.
              Work with linear, quadratic, cubic, absolute value, and square root functions.
            </p>

            <div class="space-y-2">
              <div class="flex items-center space-x-2 text-sm text-gray-500 dark:text-gray-400">
                <Icon name="i-lucide-check-circle" class="w-4 h-4 text-green-500" />
                <span>Multiple function types</span>
              </div>
              <div class="flex items-center space-x-2 text-sm text-gray-500 dark:text-gray-400">
                <Icon name="i-lucide-check-circle" class="w-4 h-4 text-green-500" />
                <span>Domain and range analysis</span>
              </div>
              <div class="flex items-center space-x-2 text-sm text-gray-500 dark:text-gray-400">
                <Icon name="i-lucide-check-circle" class="w-4 h-4 text-green-500" />
                <span>Random problem generation</span>
              </div>
            </div>

            <UButton
              @click="startGame('domain-range')"
              size="lg"
              color="success"
              class="w-full"
              :loading="loading"
            >
              Start Assessment Tool
            </UButton>
          </div>
        </UCard>
      </div>

      <!-- Features Section -->
      <div class="mt-16 text-center">
        <h3 class="text-2xl font-bold text-gray-900 dark:text-white mb-8">
          Assessment Features
        </h3>
        <div class="grid md:grid-cols-3 gap-6 max-w-3xl mx-auto">
          <div class="text-center">
            <div class="w-16 h-16 bg-purple-100 dark:bg-purple-900 rounded-full flex items-center justify-center mx-auto mb-4">
              <Icon name="i-lucide-presentation" class="w-8 h-8 text-purple-600 dark:text-purple-400" />
            </div>
            <h4 class="font-semibold text-gray-900 dark:text-white mb-2">Teacher-Led Display</h4>
            <p class="text-sm text-gray-600 dark:text-gray-300">
              Large format display perfect for classroom instruction
            </p>
          </div>
          <div class="text-center">
            <div class="w-16 h-16 bg-orange-100 dark:bg-orange-900 rounded-full flex items-center justify-center mx-auto mb-4">
              <Icon name="i-lucide-shuffle" class="w-8 h-8 text-orange-600 dark:text-orange-400" />
            </div>
            <h4 class="font-semibold text-gray-900 dark:text-white mb-2">Random Generation</h4>
            <p class="text-sm text-gray-600 dark:text-gray-300">
              Unlimited unique problems for varied assessment
            </p>
          </div>
          <div class="text-center">
            <div class="w-16 h-16 bg-pink-100 dark:bg-pink-900 rounded-full flex items-center justify-center mx-auto mb-4">
              <Icon name="i-lucide-tablet" class="w-8 h-8 text-pink-600 dark:text-pink-400" />
            </div>
            <h4 class="font-semibold text-gray-900 dark:text-white mb-2">Classroom Ready</h4>
            <p class="text-sm text-gray-600 dark:text-gray-300">
              Optimized for classroom use on tablets and projectors
            </p>
          </div>
        </div>
      </div>
    </UContainer>
  </div>
</template>

<script setup lang="ts">
import type { GameMode } from '~/types/game'

// SEO Meta
useSeoMeta({
  title: 'Math Assessment Center - Teacher-Led Mathematical Assessment',
  description: 'Teacher-led assessment tools for mathematical notation and function analysis. Display-only tools for classroom instruction and student evaluation.',
  ogTitle: 'Math Assessment Center - Teacher-Led Mathematical Assessment',
  ogDescription: 'Teacher-led assessment tools for mathematical notation and function analysis.',
})

// State
const loading = ref(false)

// Actions
const startGame = async (mode: GameMode) => {
  loading.value = true

  try {
    // Navigate to the appropriate game page
    await navigateTo(`/${mode}`)
  } catch (error) {
    console.error('Navigation error:', error)
  } finally {
    loading.value = false
  }
}
</script>