// Test file to verify mathematical calculations
// This is a temporary file for testing mathematical logic

// Import the functions we want to test (simulated here)
function formatFraction(numerator, denominator) {
  // Handle special cases
  if (denominator === 0) return 'undefined'
  if (numerator === 0) return '0'

  // Handle negative fractions
  const isNegative = (numerator < 0) !== (denominator < 0)
  numerator = Math.abs(numerator)
  denominator = Math.abs(denominator)

  // Find GCD to simplify fraction
  const gcd = (a, b) => b === 0 ? a : gcd(b, a % b)
  const commonDivisor = gcd(numerator, denominator)

  numerator = numerator / commonDivisor
  denominator = denominator / commonDivisor

  // Format the result
  const sign = isNegative ? '-' : ''

  if (denominator === 1) {
    return `${sign}${numerator}`
  }

  return `${sign}${numerator}/${denominator}`
}

function calculateQuadraticVertex(a, b, c) {
  // Calculate vertex y-coordinate: y = (4ac - b²)/(4a)
  const vertex_y_numerator = -b * b + 4 * a * c  // This is 4ac - b²
  const vertex_y_denominator = 4 * a
  return formatFraction(vertex_y_numerator, vertex_y_denominator)
}

// Test cases for quadratic vertex calculation
console.log('=== Quadratic Vertex Tests ===')

// Test 1: f(x) = x² - 4x + 3 (a=1, b=-4, c=3)
// Vertex should be at x = 4/2 = 2, y = 1(4) - 4(2) + 3 = 4 - 8 + 3 = -1
let result1 = calculateQuadraticVertex(1, -4, 3)
console.log('f(x) = x² - 4x + 3, vertex y =', result1, '(expected: -1)')

// Test 2: f(x) = 2x² + 4x + 1 (a=2, b=4, c=1)
// Vertex should be at x = -4/4 = -1, y = 2(1) + 4(-1) + 1 = 2 - 4 + 1 = -1
let result2 = calculateQuadraticVertex(2, 4, 1)
console.log('f(x) = 2x² + 4x + 1, vertex y =', result2, '(expected: -1)')

// Test 3: f(x) = -x² + 2x + 3 (a=-1, b=2, c=3)
// Vertex should be at x = -2/(-2) = 1, y = -(1) + 2(1) + 3 = -1 + 2 + 3 = 4
let result3 = calculateQuadraticVertex(-1, 2, 3)
console.log('f(x) = -x² + 2x + 3, vertex y =', result3, '(expected: 4)')

// Test 4: f(x) = x² (a=1, b=0, c=0)
// Vertex should be at x = 0, y = 0
let result4 = calculateQuadraticVertex(1, 0, 0)
console.log('f(x) = x², vertex y =', result4, '(expected: 0)')

console.log('\n=== Fraction Formatting Tests ===')

// Test fraction simplification
console.log('formatFraction(8, 12) =', formatFraction(8, 12), '(expected: 2/3)')
console.log('formatFraction(-15, 25) =', formatFraction(-15, 25), '(expected: -3/5)')
console.log('formatFraction(0, 5) =', formatFraction(0, 5), '(expected: 0)')
console.log('formatFraction(7, 1) =', formatFraction(7, 1), '(expected: 7)')
console.log('formatFraction(5, 0) =', formatFraction(5, 0), '(expected: undefined)')

console.log('\n=== Manual Vertex Verification ===')

// Manual calculation for f(x) = x² - 4x + 3
// Using vertex formula: y = c - b²/(4a) = 3 - 16/4 = 3 - 4 = -1
console.log('Manual calculation for x² - 4x + 3:')
console.log('y = c - b²/(4a) = 3 - (-4)²/(4×1) = 3 - 16/4 = 3 - 4 = -1')

// Using the code's formula: y = (4ac - b²)/(4a)
console.log('Code formula: y = (4ac - b²)/(4a) = (4×1×3 - (-4)²)/(4×1) = (12 - 16)/4 = -4/4 = -1')
console.log('Both methods give the same result: -1 ✓')
