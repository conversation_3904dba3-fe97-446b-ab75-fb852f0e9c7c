// Test the fixed formatConstant function

function formatConstantFixed(constant, forceSign = false) {
  if (constant === 0 && !forceSign) return ''
  if (constant === 0 && forceSign) return ` + 0`
  if (constant > 0) return ` + ${constant}`
  return ` - ${Math.abs(constant)}`
}

function formatCoeff(coeff) {
  if (coeff === 1) return ''
  if (coeff === -1) return '-'
  return coeff.toString()
}

function formatTerm(coeff, variable) {
  if (coeff === 0) return ''
  if (coeff > 0) return ` + ${coeff === 1 ? '' : coeff}${variable}`
  return ` - ${coeff === -1 ? '' : Math.abs(coeff)}${variable}`
}

console.log('=== Testing FIXED formatConstant ===')
console.log('formatConstant(5, false) =', `"${formatConstantFixed(5, false)}"`, '(expected: " + 5")')
console.log('formatConstant(5, true) =', `"${formatConstantFixed(5, true)}"`, '(expected: " + 5")')
console.log('formatConstant(-3, false) =', `"${formatConstantFixed(-3, false)}"`, '(expected: " - 3")')
console.log('formatConstant(-3, true) =', `"${formatConstantFixed(-3, true)}"`, '(expected: " - 3")')
console.log('formatConstant(0, false) =', `"${formatConstantFixed(0, false)}"`, '(expected: "")')
console.log('formatConstant(0, true) =', `"${formatConstantFixed(0, true)}"`, '(expected: " + 0")')

console.log('\n=== Testing equation building with FIXED functions ===')

function buildEquation(a, b, c) {
  return `f(x) = ${formatCoeff(a)}x²${formatTerm(b, 'x')}${formatConstantFixed(c)}`
}

function buildAbsoluteValue(a, h, k) {
  return `f(x) = ${formatCoeff(a)}|x${formatConstantFixed(-h, true)}|${formatConstantFixed(k)}`
}

function buildSquareRoot(a, h, k) {
  return `f(x) = ${formatCoeff(a)}√(x${formatConstantFixed(-h, true)})${formatConstantFixed(k)}`
}

console.log('Quadratic equations:')
console.log('f(x) = x² + 3x + 5:', buildEquation(1, 3, 5))
console.log('f(x) = x² + 3x - 2:', buildEquation(1, 3, -2))
console.log('f(x) = 2x² - x + 7:', buildEquation(2, -1, 7))
console.log('f(x) = x² - 4x:', buildEquation(1, -4, 0))

console.log('\nAbsolute value equations:')
console.log('f(x) = |x + 2| + 3 (h=-2, k=3):', buildAbsoluteValue(1, -2, 3))
console.log('f(x) = 2|x - 1| - 4 (h=1, k=-4):', buildAbsoluteValue(2, 1, -4))
console.log('f(x) = |x| + 5 (h=0, k=5):', buildAbsoluteValue(1, 0, 5))
console.log('f(x) = -|x + 3| (h=-3, k=0):', buildAbsoluteValue(-1, -3, 0))

console.log('\nSquare root equations:')
console.log('f(x) = √(x + 2) + 3 (h=-2, k=3):', buildSquareRoot(1, -2, 3))
console.log('f(x) = 2√(x - 1) - 4 (h=1, k=-4):', buildSquareRoot(2, 1, -4))
console.log('f(x) = √(x) + 5 (h=0, k=5):', buildSquareRoot(1, 0, 5))
console.log('f(x) = -√(x + 3) (h=-3, k=0):', buildSquareRoot(-1, -3, 0))
