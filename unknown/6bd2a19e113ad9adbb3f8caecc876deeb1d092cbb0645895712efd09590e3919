<template>
  <div class="min-h-screen bg-gradient-to-br from-green-50 to-emerald-100 dark:from-green-900/20 dark:to-emerald-900/20">
    <div class="container mx-auto px-4 py-8">
      <!-- Header -->
      <div class="text-center mb-8">
        <UButton
          @click="$router.push('/')"
          variant="ghost"
          icon="i-lucide-arrow-left"
          size="lg"
          class="mb-6"
        >
          Back to Home
        </UButton>

        <h1 class="text-4xl font-bold text-green-900 dark:text-green-100 mb-4">
          Domain & Range Assessment
        </h1>
        <p class="text-xl text-green-700 dark:text-green-300 max-w-2xl mx-auto">
          Teacher-led assessment tool for domain and range practice
        </p>
      </div>

      <!-- Display Mode -->
      <DomainRangeDisplayMode />
    </div>
  </div>
</template>

<script setup lang="ts">
// SEO Meta
useSeoMeta({
  title: 'Domain & Range Assessment - Math Assessment Center',
  description: 'Teacher-led assessment tool for domain and range practice with various mathematical functions.',
})

// Store
const gameStore = useGameStore()

// Initialize game on mount
onMounted(() => {
  gameStore.initializeGame('domain-range')
})
</script>