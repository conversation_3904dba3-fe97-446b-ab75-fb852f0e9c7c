<template>
  <div class="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 dark:from-blue-900/20 dark:to-indigo-900/20">
    <div class="container mx-auto px-4 py-8">
      <!-- Header -->
      <div class="text-center mb-8">
        <UButton
          @click="$router.push('/')"
          variant="ghost"
          icon="i-lucide-arrow-left"
          size="lg"
          class="mb-6"
        >
          Back to Home
        </UButton>

        <h1 class="text-4xl font-bold text-blue-900 dark:text-blue-100 mb-4">
          Notation Conversion Assessment
        </h1>
        <p class="text-xl text-blue-700 dark:text-blue-300 max-w-2xl mx-auto">
          Teacher-led assessment tool for mathematical notation conversion practice
        </p>
      </div>

      <!-- Display Mode -->
      <NotationDisplayMode />
    </div>
  </div>
</template>

<script setup lang="ts">
// SEO Meta
useSeoMeta({
  title: 'Notation Conversion Assessment - Math Assessment Center',
  description: 'Teacher-led assessment tool for mathematical notation conversion practice between algebraic statements, interval notation, and set-builder notation.',
})

// Store
const gameStore = useGameStore()

// Initialize game on mount
onMounted(() => {
  gameStore.initializeGame('notation-conversion')
})
</script>