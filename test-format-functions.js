// Test formatting functions for mathematical expressions

function formatConstant(constant, forceSign = false) {
  if (constant === 0 && !forceSign) return ''
  if (constant > 0) return forceSign ? ` + ${constant}` : ` + ${constant}` // BUG: Both branches are identical!
  return ` - ${Math.abs(constant)}`
}

function formatConstantFixed(constant, forceSign = false) {
  if (constant === 0 && !forceSign) return ''
  if (constant > 0) return forceSign ? ` + ${constant}` : `${constant}` // FIXED: No sign when not forced
  return ` - ${Math.abs(constant)}`
}

function formatCoeff(coeff) {
  if (coeff === 1) return ''
  if (coeff === -1) return '-'
  return coeff.toString()
}

function formatTerm(coeff, variable) {
  if (coeff === 0) return ''
  if (coeff > 0) return ` + ${coeff === 1 ? '' : coeff}${variable}`
  return ` - ${coeff === -1 ? '' : Math.abs(coeff)}${variable}`
}

console.log('=== Testing formatConstant (BUGGY VERSION) ===')
console.log('formatConstant(5, false) =', `"${formatConstant(5, false)}"`, '(expected: "5" for first term)')
console.log('formatConstant(5, true) =', `"${formatConstant(5, true)}"`, '(expected: " + 5" when forced)')
console.log('formatConstant(-3, false) =', `"${formatConstant(-3, false)}"`, '(expected: " - 3")')
console.log('formatConstant(0, false) =', `"${formatConstant(0, false)}"`, '(expected: "")')
console.log('formatConstant(0, true) =', `"${formatConstant(0, true)}"`, '(expected: " + 0" when forced)')

console.log('\n=== Testing formatConstant (FIXED VERSION) ===')
console.log('formatConstantFixed(5, false) =', `"${formatConstantFixed(5, false)}"`, '(expected: "5" for first term)')
console.log('formatConstantFixed(5, true) =', `"${formatConstantFixed(5, true)}"`, '(expected: " + 5" when forced)')
console.log('formatConstantFixed(-3, false) =', `"${formatConstantFixed(-3, false)}"`, '(expected: " - 3")')
console.log('formatConstantFixed(0, false) =', `"${formatConstantFixed(0, false)}"`, '(expected: "")')
console.log('formatConstantFixed(0, true) =', `"${formatConstantFixed(0, true)}"`, '(expected: " + 0" when forced)')

console.log('\n=== Testing equation building ===')

// Test building equations with current (buggy) formatConstant
function buildEquationBuggy(a, b, c) {
  return `f(x) = ${formatCoeff(a)}x²${formatTerm(b, 'x')}${formatConstant(c)}`
}

// Test building equations with fixed formatConstant
function buildEquationFixed(a, b, c) {
  return `f(x) = ${formatCoeff(a)}x²${formatTerm(b, 'x')}${formatConstantFixed(c)}`
}

console.log('Equation f(x) = x² + 3x + 5:')
console.log('Buggy:', buildEquationBuggy(1, 3, 5))
console.log('Fixed:', buildEquationFixed(1, 3, 5))

console.log('\nEquation f(x) = x² + 3x - 2:')
console.log('Buggy:', buildEquationBuggy(1, 3, -2))
console.log('Fixed:', buildEquationFixed(1, 3, -2))

console.log('\nEquation f(x) = 2x² - x + 7:')
console.log('Buggy:', buildEquationBuggy(2, -1, 7))
console.log('Fixed:', buildEquationFixed(2, -1, 7))
