import type {
  GameMode
} from '~/types/game'


export const useGameStore = defineStore('game', () => {
  // State - simplified for display mode
  const currentMode = ref<GameMode | null>(null)

  // Getters
  const isGameActive = computed(() => currentMode.value !== null)

  // Actions - simplified for display mode
  const initializeGame = (mode: GameMode) => {
    currentMode.value = mode
  }

  const resetGame = () => {
    currentMode.value = null
  }

  return {
    // State
    currentMode: readonly(currentMode),

    // Getters
    isGameActive,

    // Actions
    initializeGame,
    resetGame
  }
})