<template>
  <div class="max-w-4xl mx-auto">
    <!-- Display Card -->
    <UCard class="mb-6">
      <template #header>
        <div class="flex items-center justify-between">
          <h2 class="text-2xl font-semibold text-gray-900 dark:text-white">
            Domain & Range Practice
          </h2>
          <UBadge color="primary" variant="soft" size="lg">
            Display Mode
          </UBadge>
        </div>
      </template>

      <div class="space-y-8">
        <!-- Question Type -->
        <div class="text-center">
          <div class="text-lg font-medium text-gray-600 dark:text-gray-400 mb-2">
            Task
          </div>
          <UBadge 
            :color="getQuestionColor(currentProblem?.questionType)" 
            variant="soft" 
            size="lg"
            class="text-lg px-4 py-2 font-extrabold"
          >
            {{ currentProblem?.description }}
          </UBadge>
        </div>

        <!-- Function Display -->
        <div class="text-center">
          <div class="text-lg font-medium text-gray-600 dark:text-gray-400 mb-4">
            Function
          </div>
          <div class="p-8 bg-green-50 dark:bg-green-900/20 rounded-xl border-2 border-green-200 dark:border-green-700">
            <div class="text-green-900 dark:text-green-100 function-equation leading-relaxed">
              {{ currentProblem?.equation }}
            </div>
          </div>
        </div>

        <!-- Instructions -->
        <div class="text-center">
          <p class="text-gray-600 dark:text-gray-400 text-lg">
            This function is displayed for teacher-led assessment and discussion.
          </p>
        </div>
      </div>

      <template #footer>
        <div class="flex justify-center gap-4">
          <UButton
            @click="toggleAnswer"
            size="lg"
            :icon="showAnswer ? 'i-lucide-eye-off' : 'i-lucide-eye'"
            variant="outline"
          >
            {{ showAnswer ? 'Hide Answer' : 'View Answer' }}
          </UButton>

          <UButton
            @click="generateNewProblem"
            size="lg"
            icon="i-lucide-refresh-cw"
            :loading="generating"
          >
            Generate New Problem
          </UButton>
        </div>
      </template>
    </UCard>

    <!-- Answer Display -->
    <UCard v-if="showAnswer && currentProblem" class="mt-6 border-2 border-green-200 dark:border-green-800">
      <template #header>
        <div class="flex items-center space-x-3">
          <div class="w-8 h-8 bg-green-100 dark:bg-green-900 rounded-full flex items-center justify-center">
            <Icon name="i-lucide-check-circle" class="w-5 h-5 text-green-600 dark:text-green-400" />
          </div>
          <h3 class="text-xl font-semibold text-green-900 dark:text-green-100">
            Correct Answers
          </h3>
        </div>
      </template>

      <div class="space-y-4">
        <div class="grid gap-4">
          <!-- Domain -->
          <div class="p-4 bg-blue-50 dark:bg-blue-900/20 rounded-lg">
            <div class="flex items-center space-x-2 mb-2">
              <UBadge color="primary" variant="soft" size="sm">Domain</UBadge>
            </div>
            <p class="interval-notation math-answer">
              {{ currentProblem.answer.domain }}
            </p>
          </div>

          <!-- Range -->
          <div class="p-4 bg-purple-50 dark:bg-purple-900/20 rounded-lg">
            <div class="flex items-center space-x-2 mb-2">
              <UBadge color="secondary" variant="soft" size="sm">Range</UBadge>
            </div>
            <p class="interval-notation math-answer">
              {{ currentProblem.answer.range }}
            </p>
          </div>
        </div>

        <!-- Current Question Highlight -->
        <div class="p-3 bg-yellow-50 dark:bg-yellow-900/20 rounded-lg border border-yellow-200 dark:border-yellow-800">
          <div class="flex items-center space-x-2 mb-1">
            <Icon name="i-lucide-target" class="w-4 h-4 text-yellow-600 dark:text-yellow-400" />
            <span class="text-sm font-medium text-yellow-800 dark:text-yellow-200">
              Current Question: {{ currentProblem.description }}
            </span>
          </div>
          <p class="interval-notation text-lg font-semibold text-yellow-900 dark:text-yellow-100">
            {{ currentProblem.questionType === 'domain' ? currentProblem.answer.domain : currentProblem.answer.range }}
          </p>
        </div>
      </div>
    </UCard>

    <!-- Problem History (Optional) -->
    <UCard v-if="problemHistory.length > 1">
      <template #header>
        <h3 class="text-lg font-semibold text-gray-900 dark:text-white">
          Recent Problems
        </h3>
      </template>
      
      <div class="space-y-3">
        <div 
          v-for="(problem, index) in problemHistory.slice(-5).reverse()" 
          :key="problem.id"
          class="flex items-center justify-between p-3 bg-gray-50 dark:bg-gray-800 rounded-lg"
        >
          <div class="flex items-center space-x-3">
            <UBadge 
              :color="getQuestionColor(problem.questionType)" 
              variant="soft"
              size="sm"
            >
              {{ problem.description }}
            </UBadge>
            <span class="function-equation text-sm text-gray-700 dark:text-gray-300">
              {{ problem.equation }}
            </span>
          </div>
          <span class="text-xs text-gray-500 dark:text-gray-400">
            {{ index === 0 ? 'Current' : `${index + 1} ago` }}
          </span>
        </div>
      </div>
    </UCard>
  </div>
</template>

<script setup lang="ts">
import type { DisplayDomainRangeProblem, FunctionType } from '~/types/game'
import { generateDisplayDomainRangeProblem } from '~/utils/problemGenerator'



// State
const currentProblem = ref<DisplayDomainRangeProblem | null>(null)
const generating = ref(false)
const problemHistory = ref<DisplayDomainRangeProblem[]>([])
const showAnswer = ref(false)

// Methods
const generateNewProblem = async () => {
  generating.value = true
  
  try {
    // Small delay for better UX
    await new Promise(resolve => setTimeout(resolve, 200))
    
    const newProblem = generateDisplayDomainRangeProblem()
    
    // Add current problem to history before replacing
    if (currentProblem.value) {
      problemHistory.value.push(currentProblem.value)
    }
    
    currentProblem.value = newProblem
    // Hide answer when generating new problem
    showAnswer.value = false
  } finally {
    generating.value = false
  }
}

const toggleAnswer = () => {
  showAnswer.value = !showAnswer.value
}

const getQuestionColor = (questionType?: string) => {
  switch (questionType) {
    case 'domain':
      return 'primary'
    case 'range':
      return 'secondary'
    default:
      return 'neutral'
  }
}

const getFunctionTypeColor = (functionType: FunctionType) => {
  switch (functionType) {
    case 'linear':
      return 'success'
    case 'quadratic':
      return 'primary'
    case 'cubic':
      return 'secondary'
    case 'absolute':
      return 'warning'
    case 'square-root':
      return 'error'
    default:
      return 'neutral'
  }
}

const getFunctionTypeName = (functionType: FunctionType) => {
  switch (functionType) {
    case 'linear':
      return 'Linear Function'
    case 'quadratic':
      return 'Quadratic Function'
    case 'cubic':
      return 'Cubic Function'
    case 'absolute':
      return 'Absolute Value Function'
    case 'square-root':
      return 'Square Root Function'
    default:
      return 'Function'
  }
}

// Initialize with first problem
onMounted(() => {
  generateNewProblem()
})
</script>
